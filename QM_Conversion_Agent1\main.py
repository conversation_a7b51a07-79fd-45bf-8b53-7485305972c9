import sys
import uuid
from typing import Dict, Any
from config import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)
from workflow import GraphBuilder
from dotenv import load_dotenv
load_dotenv()


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """
    Create and initialize a Language Model instance based on the specified provider.

    This function supports multiple LLM providers including Azure OpenAI, OpenAI, Anthropic,
    Groq, Gemini, and Ollama. Each provider is initialized with the appropriate configuration
    settings from the config manager.

    Args:
        provider: The LLM provider name (azure_openai, openai, anthropic, groq, gemini, ollama)
        config_manager: Configuration manager containing provider-specific settings

    Returns:
        An initialized instance of the appropriate LLM class ready for use

    Raises:
        ValueError: If the provider is not supported or recognized
    """
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")


def setup_application() -> Any:
    """
    Set up the application with configuration and initialize the Language Model.

    This function handles the complete application setup process including loading
    configuration settings and initializing the appropriate LLM provider based
    on the configuration.

    Returns:
        An initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str) -> Dict[str, Any]:
    """
    Execute the complete Oracle to PostgreSQL database migration workflow.

    This function orchestrates the entire migration process including error analysis,
    source mapping, statement conversion, and validation using AI-driven techniques.
    The workflow handles iterative improvements and maintains detailed tracking.

    Args:
        llm: Initialized Language Model instance for AI-driven analysis
        source_code: Original Oracle database code to be migrated
        target_code: PostgreSQL target code with potential deployment errors
        deployment_error: Error message from PostgreSQL deployment attempt

    Returns:
        Dictionary containing workflow execution results and final state
    """
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()
    graph_builder.save_graph_image(graph_builder.graph)


    # Create a unique thread ID for this workflow execution
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    result = graph_builder.invoke_graph({
        "source_code": source_code,
        "target_code": target_code,
        "deployment_error": deployment_error,
        "iteration_count": 1  # Initialize iteration count to 1
    }, thread_id=thread_id)
    return result


def main():
    try:
        # Sample data for testing - in a real application, these would be provided by the user
        source_code = """

  CREATE OR REPLACE  PROCEDURE "BILLING"."P_ACKNOWLEDGECASHTOCOUNTER" 
 (
        IC_UPDATEACK  IN CLOB,
        OC_BALANCE    OUT SYS_REFCURSOR
 )
as
LV_UPDATEACK      XMLTYPE;
LV_LOCATIONID     NATURALACCOUNTMASTER.LOCATIONID%type;
LV_COMPANYID      NATURALACCOUNTMASTER.COMPANYID%type;
LV_UPDATEDBY      TRANSACTION.UPDATEDBY%type;
LV_TRANSACTIONID  TRANSACTION.TRANSACTIONID%type;
LV_TRANAMOUNT     TRANSACTION.TRANAMOUNT%type;
LV_TRANEVENT     TRANSACTION.TRANEVENT%type;
begin
LV_UPDATEACK      :=XMLTYPE(IC_UPDATEACK);
LV_TRANSACTIONID  :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@TransactionNo').GETSTRINGVAL());
LV_LOCATIONID     :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@LocationID').GETSTRINGVAL());
LV_COMPANYID      :=TRIM(LV_UPDATEACK.EXTRACT('//CashHandlingDetails/@CompanyID').GETSTRINGVAL());
LV_UPDATEDBY      :=TRIM(LV_UPDATEACK.EXTRACT('//LoginID/text()').GETSTRINGVAL());

SAVEPOINT S_SAVEPOINT;

SELECT  DISTINCT TRANEVENT INTO LV_TRANEVENT
FROM    TRANSACTION
WHERE   TRANSACTIONID=LV_TRANSACTIONID
AND     ENTRYSTATUS='ENTERED'
AND     (REVERSALFLAG=1 AND DELETEFLAG=1);



CASE

--------------------START ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER

    WHEN LV_TRANEVENT='CASHTRANSFERCASHINHANDTOCOUNTER' THEN

       DECLARE
            LV_CASHACCOUNT    VARCHAR2(20);
            LV_CASHINHAND     VARCHAR2(20);
       BEGIN
        UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  ACCOUNTNO,TRANAMOUNT INTO LV_CASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR';

        SELECT  DISTINCT ACCOUNTNO INTO LV_CASHINHAND
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR';

        UPDATE  NATURALACCOUNTMASTER
         SET    CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_CASHINHAND
         AND    ACCOUNTTYPE='CASHINHAND'
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_CASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    NATURALACCOUNTMASTER
          WHERE   ACCOUNTID=LV_CASHINHAND
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_CASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER

----------------------START ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER

 WHEN LV_TRANEVENT='CASHTRANSFERCOUNTERTOCOUNTER' THEN

    DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_TOCASHACCOUNT      VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  ACCOUNTNO,TRANAMOUNT INTO LV_FROMCASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR';

        SELECT  DISTINCT ACCOUNTNO INTO LV_TOCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR';

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
         WHERE  ACCOUNTID=LV_TOCASHACCOUNT
         AND    LOCATIONID=LV_LOCATIONID
         AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_TOCASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID=LV_FROMCASHACCOUNT
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER

----------------------START ACKNOWLEDGE CASH HANDOVER TO COUNTER

 WHEN LV_TRANEVENT='CASHHANDOVERTOCOUNTER' THEN

      DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_TOCASHACCOUNT      VARCHAR2(20);
            LV_FROMNONCASHACCOUNT VARCHAR2(20);
            LV_TONONCASHACCOUNT   VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  DISTINCT ACCOUNTNO,TRANAMOUNT INTO LV_TOCASHACCOUNT,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE='CASH';

        UPDATE  CASHACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_TOCASHACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;


        SELECT  DISTINCT ACCOUNTNO INTO LV_TONONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';
        SELECT  SUM(TRANAMOUNT) INTO LV_TRANAMOUNT FROM TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMNONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE!='CASH';

        UPDATE  CASHACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_TONONCASHACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMNONCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID IN (LV_TOCASHACCOUNT,LV_TONONCASHACCOUNT,LV_FROMCASHACCOUNT,LV_FROMNONCASHACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;

      END;

----------------------END ACKNOWLEDGE CASH HANDOVER TO COUNTER

----------------------START ACKNOWLEDGE CASH HANDOVER TO CASHINHAND

 WHEN LV_TRANEVENT='CASHHANDOVERTOCASHINHAND' THEN
       DECLARE
            LV_FROMCASHACCOUNT    VARCHAR2(20);
            LV_CASHINHAND         VARCHAR2(20);
            LV_FROMNONCASHACCOUNT VARCHAR2(20);
            LV_BANKACCOUNT        VARCHAR2(20);
      BEGIN
      UPDATE  TRANSACTION
                SET   ENTRYSTATUS='POSTED',
                      UPDATEDBY=LV_UPDATEDBY,
                      UPDATEDDATE=SYSDATE
                WHERE TRANSACTIONID=LV_TRANSACTIONID;

        SELECT  DISTINCT ACCOUNTNO,TRANAMOUNT INTO LV_CASHINHAND,LV_TRANAMOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE='CASH';

       UPDATE  NATURALACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_CASHINHAND
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;


        SELECT  DISTINCT ACCOUNTNO INTO LV_BANKACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

       SELECT  SUM(TRANAMOUNT) INTO LV_TRANAMOUNT FROM TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='DR' AND PAYMENTMODE!='CASH';

        SELECT  DISTINCT ACCOUNTNO INTO LV_FROMNONCASHACCOUNT
        FROM    TRANSACTION
        WHERE   TRANSACTIONID=LV_TRANSACTIONID AND TRANTYPE='CR' AND PAYMENTMODE!='CASH';

        UPDATE  NATURALACCOUNTMASTER
            SET  CUMDEBITAMOUNT=NVL(CUMDEBITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
            WHERE  ACCOUNTID=LV_BANKACCOUNT
            AND    LOCATIONID=LV_LOCATIONID
            AND    COMPANYID=LV_COMPANYID;

        UPDATE  CASHACCOUNTMASTER
           SET  CUMCREDITAMOUNT=NVL(CUMCREDITAMOUNT,0)+LV_TRANAMOUNT,
                UPDATEDBY=LV_UPDATEDBY,
                UPDATEDDATE=SYSDATE
          WHERE  ACCOUNTID=LV_FROMNONCASHACCOUNT
          AND    LOCATIONID=LV_LOCATIONID
          AND    COMPANYID=LV_COMPANYID;

          OPEN    OC_BALANCE FOR
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    NATURALACCOUNTMASTER
          WHERE   ACCOUNTID IN (LV_CASHINHAND,LV_BANKACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID

          UNION
          SELECT  ACCOUNTID,ACCOUNTNAME,(nvl(CUMDEBITAMOUNT,0)-nvl(CUMCREDITAMOUNT,0)) BALANCE
          FROM    CASHACCOUNTMASTER
          WHERE   ACCOUNTID IN(LV_FROMCASHACCOUNT,LV_FROMNONCASHACCOUNT)
          AND     COMPANYID=LV_LOCATIONID
          AND     LOCATIONID=LV_LOCATIONID;
      END;
----------------------END ACKNOWLEDGE CASH HANDOVER TO CASHINHAND

 END CASE;


EXCEPTION WHEN OTHERS THEN
DBMS_OUTPUT.put_line(SQLCODE||SQLERRM);
ROLLBACK TO S_SAVEPOINT;

end p_acknowledgecashtocounter;
        """

        target_code = """
SET search_path TO BILLING;

CREATE OR REPLACE PROCEDURE billing.P_ACKNOWLEDGECASHTOCOUNTER (IC_UPDATEACK IN text, OC_BALANCE INOUT refcursor)
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    
    LV_UPDATEACK xml;
    LV_LOCATIONID billing.NATURALACCOUNTMASTER.LOCATIONID%type;
    LV_COMPANYID billing.NATURALACCOUNTMASTER.COMPANYID%type;
    LV_UPDATEDBY billing.TRANSACTION.UPDATEDBY%type;
    LV_TRANSACTIONID billing.TRANSACTION.TRANSACTIONID%type;
    LV_TRANAMOUNT billing.TRANSACTION.TRANAMOUNT%type;
    LV_TRANEVENT billing.TRANSACTION.TRANEVENT%type;
BEGIN
    SET search_path TO BILLING;
    LV_UPDATEACK := xml(IC_UPDATEACK);
    LV_TRANSACTIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@TransactionNo', LV_UPDATEACK)))
        END)::text);
    LV_LOCATIONID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@LocationID', LV_UPDATEACK)))
        END)::text);
    LV_COMPANYID := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//CashHandlingDetails/@CompanyID', LV_UPDATEACK)))
        END)::text);
    LV_UPDATEDBY := TRIM((
        CASE WHEN (
            SELECT
                unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('//LoginID/text(', LV_UPDATEACK)))::text)::text
        END);
    SAVEPOINT S_SAVEPOINT;
    SELECT DISTINCT
        TRANEVENT INTO STRICT LV_TRANEVENT
    FROM
        billing.TRANSACTION
    WHERE
        TRANSACTIONID = LV_TRANSACTIONID
        AND ENTRYSTATUS = 'ENTERED'
        AND (REVERSALFLAG = 1
            AND DELETEFLAG = 1);
    CASE
    --START ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCASHINHANDTOCOUNTER' THEN
        DECLARE LV_CASHACCOUNT varchar(20); LV_CASHINHAND varchar(20);
        BEGIN
            UPDATE
                billing.TRANSACTION
            SET
                ENTRYSTATUS = 'POSTED',
                UPDATEDBY = LV_UPDATEDBY,
                UPDATEDDATE = current_timestamp(0)::timestamp
            WHERE
                TRANSACTIONID = LV_TRANSACTIONID;
                SELECT
                    ACCOUNTNO,
                    TRANAMOUNT INTO STRICT LV_CASHACCOUNT,
                    LV_TRANAMOUNT
                FROM
                    billing.TRANSACTION
                WHERE
                    TRANSACTIONID = LV_TRANSACTIONID
                    AND TRANTYPE = 'DR'; SELECT DISTINCT
                        ACCOUNTNO INTO STRICT LV_CASHINHAND
                    FROM
                        billing.TRANSACTION
                    WHERE
                        TRANSACTIONID = LV_TRANSACTIONID
                        AND TRANTYPE = 'CR'; UPDATE
                            billing.NATURALACCOUNTMASTER
                        SET
                            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
                            UPDATEDBY = LV_UPDATEDBY,
                            UPDATEDDATE = current_timestamp(0)::timestamp
                        WHERE
                            ACCOUNTID = LV_CASHINHAND
                            AND ACCOUNTTYPE = 'CASHINHAND'
                            AND LOCATIONID = LV_LOCATIONID
                            AND COMPANYID = LV_COMPANYID; UPDATE
                                billing.CASHACCOUNTMASTER
                            SET
                                CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
                                UPDATEDBY = LV_UPDATEDBY,
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                ACCOUNTID = LV_CASHACCOUNT
                                AND LOCATIONID = LV_LOCATIONID
                                AND COMPANYID = LV_COMPANYID; OPEN OC_BALANCE FOR
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.NATURALACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHINHAND
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID
                                    UNION
                                    SELECT
                                        ACCOUNTID,
                                        ACCOUNTNAME,
                                        (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
                                    FROM
                                        billing.CASHACCOUNTMASTER
                                    WHERE
                                        ACCOUNTID = LV_CASHACCOUNT
                                        AND COMPANYID = LV_LOCATIONID
                                        AND LOCATIONID = LV_LOCATIONID;
            END;
    --END ACKNOWLEDGE CASH TRANSFER CASH IN HAND TO COUNTER
    --START ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHTRANSFERCOUNTERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_TOCASHACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_FROMCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TOCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_TOCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID = LV_FROMCASHACCOUNT
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH TRANSFER COUNTER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO COUNTER
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCOUNTER' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_TOCASHACCOUNT varchar(20);
    LV_FROMNONCASHACCOUNT varchar(20);
    LV_TONONCASHACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_TOCASHACCOUNT,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TOCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_TONONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_TONONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_TOCASHACCOUNT, LV_TONONCASHACCOUNT, LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH HANDOVER TO COUNTER
    --START ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    WHEN LV_TRANEVENT = 'CASHHANDOVERTOCASHINHAND' THEN
        DECLARE LV_FROMCASHACCOUNT varchar(20);
    LV_CASHINHAND varchar(20);
    LV_FROMNONCASHACCOUNT varchar(20);
    LV_BANKACCOUNT varchar(20);
    BEGIN
        UPDATE
            billing.TRANSACTION
        SET
            ENTRYSTATUS = 'POSTED',
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID;
        SELECT DISTINCT
            ACCOUNTNO,
            TRANAMOUNT INTO STRICT LV_CASHINHAND,
            LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE = 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE = 'CASH';
        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_CASHINHAND
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_BANKACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT
            SUM(TRANAMOUNT) INTO STRICT LV_TRANAMOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'DR'
            AND PAYMENTMODE != 'CASH';
        SELECT DISTINCT
            ACCOUNTNO INTO STRICT LV_FROMNONCASHACCOUNT
        FROM
            billing.TRANSACTION
        WHERE
            TRANSACTIONID = LV_TRANSACTIONID
            AND TRANTYPE = 'CR'
            AND PAYMENTMODE != 'CASH';
        UPDATE
            billing.NATURALACCOUNTMASTER
        SET
            CUMDEBITAMOUNT = coalesce(CUMDEBITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_BANKACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        UPDATE
            billing.CASHACCOUNTMASTER
        SET
            CUMCREDITAMOUNT = coalesce(CUMCREDITAMOUNT, 0) + LV_TRANAMOUNT,
            UPDATEDBY = LV_UPDATEDBY,
            UPDATEDDATE = current_timestamp(0)::timestamp
        WHERE
            ACCOUNTID = LV_FROMNONCASHACCOUNT
            AND LOCATIONID = LV_LOCATIONID
            AND COMPANYID = LV_COMPANYID;
        OPEN OC_BALANCE FOR
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.NATURALACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_CASHINHAND, LV_BANKACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID
            UNION
            SELECT
                ACCOUNTID,
                ACCOUNTNAME,
                (coalesce(CUMDEBITAMOUNT, 0) - coalesce(CUMCREDITAMOUNT, 0)) BALANCE
            FROM
                billing.CASHACCOUNTMASTER
            WHERE
                ACCOUNTID IN (LV_FROMCASHACCOUNT, LV_FROMNONCASHACCOUNT)
                AND COMPANYID = LV_LOCATIONID
                AND LOCATIONID = LV_LOCATIONID;
    END;
    --END ACKNOWLEDGE CASH HANDOVER TO CASHINHAND
    -- END CASE
        END;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '% %', sqlstate, sqlerrm;
        ROLLBACK TO S_SAVEPOINT;
END;

$BODY$;





               """

        deployment_error = """
    SQL Error [42601]: ERROR: mismatched parentheses at or near ")"
  Position: 2013

        """

        llm = setup_application()
        run_workflow(llm, source_code, target_code, deployment_error)

        print("\n🎉 Execution completed successfully!")

    except ValueError as e:
        print(f"\nConfiguration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
